#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import threading
import logging
import uuid
from app.config.settings import TEMP_FILE_CLEANUP_DELAY, TEMP_DIR

logger = logging.getLogger(__name__)


def save_temp_file(file_content: bytes, file_extension: str = ".tmp") -> str:
    """
    保存临时文件

    Args:
        file_content: 文件内容
        file_extension: 文件扩展名

    Returns:
        str: 临时文件路径
    """
    # 确保临时目录存在
    os.makedirs(TEMP_DIR, exist_ok=True)

    # 生成唯一的文件名
    file_name = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(TEMP_DIR, file_name)

    # 保存文件
    with open(file_path, 'wb') as f:
        f.write(file_content)

    logger.info(f"已保存临时文件: {file_path}")
    return file_path

def cleanup_temp_files(file_path, delay=TEMP_FILE_CLEANUP_DELAY):
    """
    延迟一段时间后删除临时文件
    :param file_path: 文件路径
    :param delay: 延迟时间（秒）
    """
    def delete_file():
        time.sleep(delay)
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已删除临时文件: {file_path}")
        except Exception as e:
            logger.error(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
    
    # 启动后台线程删除文件
    threading.Thread(target=delete_file, daemon=True).start()

def ensure_directories():
    """确保必要的目录存在"""
    from app.config.settings import TEMP_DIR, STATIC_DIR
    
    os.makedirs(TEMP_DIR, exist_ok=True)
    os.makedirs(STATIC_DIR, exist_ok=True)

def cleanup_old_temp_files():
    """清理旧的临时文件"""
    from app.config.settings import TEMP_DIR
    
    try:
        for file_name in os.listdir(TEMP_DIR):
            file_path = os.path.join(TEMP_DIR, file_name)
            if os.path.isfile(file_path):
                os.remove(file_path)
                logger.info(f"已删除旧的临时文件: {file_path}")
    except Exception as e:
        logger.error(f"清理临时文件失败: {str(e)}")
