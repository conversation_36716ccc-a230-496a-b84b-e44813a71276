#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import time
import asyncio
import logging
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect

from app.services.websocket_manager import WebSocketConnectionManager
from app.services.ai_service import AIService
from app.config.prompts import TABLE_PROMPT

logger = logging.getLogger(__name__)

class WebSocketHandler:
    """WebSocket处理器"""
    
    def __init__(self, websocket_manager: WebSocketConnectionManager):
        self.websocket_manager = websocket_manager
        self.ai_service = AIService()
    
    async def handle_websocket_chat(self, websocket: WebSocket):
        """处理WebSocket聊天连接"""
        websocket_id = await self.websocket_manager.connect(websocket)
        logger.info(f"新的WebSocket连接已建立，ID: {websocket_id}")
        
        try:
            # 等待客户端发送初始化消息
            init_message = await websocket.receive_text()
            try:
                data = json.loads(init_message)
                # 检查是否是初始化消息
                if data.get("wsOrder") == "init_client":
                    # 收到初始化消息后，发送WebSocket ID给客户端
                    await websocket.send_json({"type": "connection_id", "id": websocket_id})
                    logger.info(f"客户端初始化完成，已发送连接ID: {websocket_id}")
                else:
                    logger.warning(f"收到非初始化消息: {init_message[:100]}")
                    await websocket.send_json({"type": "error", "data": "需要先发送初始化消息"})
            except json.JSONDecodeError as e:
                logger.error(f"初始化消息JSON解析错误: {str(e)}, 收到的数据: {init_message[:100]}")
                await websocket.send_json({"type": "error", "data": "初始化消息格式错误"})
            
            # 保持连接开放，持续处理消息
            while True:
                try:
                    # 等待接收消息
                    message = await websocket.receive_text()
                    # 更新活动时间
                    self.websocket_manager.update_activity(websocket_id)
                    
                    # 检查是否是ping/pong或close等控制消息
                    if message in ["ping", "pong", "close"]:
                        if message == "ping":
                            await websocket.send_text("pong")
                            logger.info("收到ping，回复pong")
                        elif message == "close":
                            logger.info("客户端请求关闭连接")
                            break
                        continue  # 继续下一次循环，等待新消息
                    
                    # 处理JSON聊天数据
                    await self._process_chat_message(websocket, websocket_id, message)
                    
                except asyncio.TimeoutError:
                    logger.info("WebSocket连接超时，发送心跳")
                    # 发送心跳消息，保持连接活跃
                    await websocket.send_json({"type": "ping"})
                    continue
                except WebSocketDisconnect:
                    logger.info("客户端已断开连接")
                    break
        except WebSocketDisconnect:
            logger.info("WebSocket连接已断开")
        except Exception as e:
            import traceback
            error_msg = f"{str(e)}\n{traceback.format_exc()}"
            logger.error(f"WebSocket处理时出错: {error_msg}")
            try:
                await websocket.send_json({"type": "error", "data": str(e)})
            except:
                logger.error("无法发送错误消息，连接可能已断开")
        finally:
            # 断开连接并清理
            self.websocket_manager.disconnect(websocket_id)
            logger.info(f"WebSocket连接 {websocket_id} 已结束并清理")
    
    async def _process_chat_message(self, websocket: WebSocket, websocket_id: str, message: str):
        """处理聊天消息"""
        try:
            data = json.loads(message)
            messages = data.get("messages", [])
            voice = data.get("voice", "Cherry")
            audio_format = data.get("audio_format", "wav")
            
            # 检查消息类型，默认为普通聊天
            message_type = data.get("message_type", "chat")
            logger.info(f"收到WebSocket请求，消息类型: {message_type}, 消息数量: {len(messages)}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}, 收到的数据: {message[:100]}")
            await websocket.send_json({"type": "error", "data": "请求数据格式错误"})
            return
        
        if not messages:
            await websocket.send_json({"error": "未接收到消息数据"})
            return
        
        # 处理视频消息
        processed_messages = self.ai_service.process_video_content(messages)
        
        # 根据消息类型选择不同的提示词和返回模式
        if message_type == "table":
            # 表单数据处理模式：使用table_prompt，只返回文本
            prompt_messages = TABLE_PROMPT + processed_messages
            modalities = ["text"]
            logger.info(f"表单模式，使用table_prompt，仅返回文本，直接传递用户消息(含图像)")
        else:
            # 普通聊天模式：使用DEFAULT_PROMPT，返回文本和音频
            prompt_messages = processed_messages
            modalities = ["text", "audio"]
            logger.info(f"聊天模式，使用DEFAULT_PROMPT，返回文本和音频")
        
        # 调用大模型
        completion = self.ai_service.create_completion(
            messages=prompt_messages,
            modalities=modalities,
            voice=voice,
            audio_format=audio_format,
            stream=True
        )
        
        # 准备响应数据
        text_response = ""
        full_audio_data = b''
        
        # 处理流式响应
        for chunk in completion:
            self.websocket_manager.update_activity(websocket_id)  # 在处理响应期间也更新活动时间
            if chunk.choices:
                # 处理音频内容
                if hasattr(chunk.choices[0].delta, "audio"):
                    # 从音频数据中提取
                    if "data" in chunk.choices[0].delta.audio:
                        audio_string = chunk.choices[0].delta.audio["data"]
                        # 解码base64数据
                        import base64
                        wav_bytes = base64.b64decode(audio_string)
                        # 累积完整的音频数据
                        full_audio_data += wav_bytes
                        
                        # 发送音频数据给客户端
                        await websocket.send_json({"type": "audio", "data": audio_string})
                    
                    # 处理文字内容
                    if "transcript" in chunk.choices[0].delta.audio:
                        transcript = chunk.choices[0].delta.audio["transcript"]
                        text_response += transcript
                        
                        # 发送文字数据给客户端
                        await websocket.send_json({"type": "text", "data": transcript})
            
            elif hasattr(chunk, 'usage'):
                # 发送使用统计
                await websocket.send_json({"type": "usage", "data": str(chunk.usage)})
        
        # 发送完成信号
        await websocket.send_json({
            "type": "complete", 
            "data": {
                "text": text_response
            }
        })
        
        # 更新活动时间
        self.websocket_manager.update_activity(websocket_id)
