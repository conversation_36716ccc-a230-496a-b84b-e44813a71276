#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import uuid
import asyncio
import logging
from typing import Dict, Optional
from fastapi import WebSocket
from datetime import datetime

from app.config.settings import WEBSOCKET_INACTIVE_TIMEOUT, WEBSOCKET_CLEANUP_INTERVAL

logger = logging.getLogger(__name__)

class WebSocketConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.last_activity: Dict[str, float] = {}  # 存储最后活动时间
        self.inactive_timeout = WEBSOCKET_INACTIVE_TIMEOUT
        self.cleanup_interval = WEBSOCKET_CLEANUP_INTERVAL

    async def connect(self, websocket: WebSocket) -> str:
        """
        连接WebSocket并返回唯一ID
        """
        await websocket.accept()
        websocket_id = str(uuid.uuid4())
        self.active_connections[websocket_id] = websocket
        self.last_activity[websocket_id] = time.time()  # 记录连接时间
        return websocket_id

    def disconnect(self, websocket_id: str):
        """
        断开连接并从活跃连接中移除
        """
        if websocket_id in self.active_connections:
            del self.active_connections[websocket_id]
            if websocket_id in self.last_activity:
                del self.last_activity[websocket_id]
            logger.info(f"WebSocket连接 {websocket_id} 已移除")

    def get_connection(self, websocket_id: str) -> Optional[WebSocket]:
        """
        获取WebSocket连接
        """
        return self.active_connections.get(websocket_id)

    def update_activity(self, websocket_id: str):
        """
        更新连接的最后活动时间
        """
        if websocket_id in self.active_connections:
            self.last_activity[websocket_id] = time.time()

    async def cleanup_inactive_connections(self):
        """
        清理不活跃的连接
        """
        current_time = time.time()
        inactive_ids = []
        
        # 找出所有不活跃的连接
        for websocket_id, last_active in list(self.last_activity.items()):
            if current_time - last_active > self.inactive_timeout:
                inactive_ids.append(websocket_id)
        
        # 断开不活跃的连接
        for websocket_id in inactive_ids:
            logger.info(f"断开不活跃的WebSocket连接: {websocket_id}, 超过 {self.inactive_timeout} 秒无活动")
            try:
                websocket = self.active_connections.get(websocket_id)
                if websocket:
                    await websocket.close(code=1000, reason="不活跃超时")
                self.disconnect(websocket_id)
            except Exception as e:
                logger.error(f"关闭不活跃连接时出错: {str(e)}")
                self.disconnect(websocket_id)  # 强制从列表中移除

    async def start_cleanup_task(self):
        """
        启动定期清理任务的后台任务
        """
        while True:
            await asyncio.sleep(self.cleanup_interval)
            try:
                await self.cleanup_inactive_connections()
            except Exception as e:
                logger.error(f"清理任务出错: {str(e)}")

    def get_connection_info(self):
        """
        获取所有活跃连接的信息
        """
        current_time = time.time()
        connection_info = []
        
        for websocket_id, websocket in self.active_connections.items():
            last_active = self.last_activity.get(websocket_id, 0)
            inactive_seconds = current_time - last_active
            connection_info.append({
                "id": websocket_id,
                "last_active": datetime.fromtimestamp(last_active).strftime('%Y-%m-%d %H:%M:%S'),
                "inactive_for": f"{inactive_seconds:.1f}秒",
                "is_stale": inactive_seconds > self.inactive_timeout
            })
        
        return {
            "count": len(self.active_connections),
            "timeout_setting": f"{self.inactive_timeout}秒",
            "connections": connection_info
        }
