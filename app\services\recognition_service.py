from app.models.recognition_models import RecognitionResponse
from app.utils.qwen_vl_utils import request_qwen_vl, request_qwen_vl_with_binary
from app.utils.validator import validate_image_url
import base64
from app.models.recognition_models import BatchMaterialVerificationRequest


def recognize_license_by_url_service(request):
    """
    通过图片 URL 识别证照的服务方法。

    Args:
        request (RecognizeLicenseByUrlRequest): 包含图片 URL、提示词和附加提示词的请求对象。

    Returns:
        RecognitionResponse: 识别结果对象。

    Raises:
        ValueError: 当 image_url 格式无效时抛出。
        Exception: 当识别过程中发生其他错误时抛出。
    """
    try:
        if not validate_image_url(request.image_url):
            raise ValueError('image_url 必须以 http:// 或 https:// 开头')

        result = request_qwen_vl(
            image_path=request.image_url,
            prompt=request.prompt,
            additional_prompt=request.additional_prompt
        )

        return RecognitionResponse(
            code=result.get('code', 500),
            info=result.get('info', '识别失败'),
            data=result.get('data')
        )
    except ValueError: 
        raise
    except Exception as e:
        raise ValueError(str(e))


def recognize_license_by_binary_service(binary_data, prompt, additional_prompt):
    """
    通过二进制图片数据识别证照的服务方法。

    Args:
        binary_data (UploadFile): 图片文件对象。
        prompt (str): 提示词。
        additional_prompt (str): 附加提示词。

    Returns:
        RecognitionResponse: 识别结果对象。
    """
    try:
        image_binary = binary_data.file.read()
        result = request_qwen_vl_with_binary(
            image_binary=image_binary,
            mime_type=binary_data.content_type or 'image/jpeg',
            prompt=prompt,
            additional_prompt=additional_prompt
        )
        return RecognitionResponse(
            code=result.get('code', 500),
            info=result.get('info', '识别失败'),
            data=result.get('data')
        )
    except Exception as e:
        return RecognitionResponse(
            code=500,
            info=f'识别过程中发生错误: {str(e)}',
            data=None
        )


def recognize_license_by_base64_service(request):
    """
    通过 Base64 编码的图片数据识别证照的服务方法。

    Args:
        request (RecognizeLicenseByBinaryRequest): 包含 Base64 数据、提示词和附加提示词的请求对象。

    Returns:
        RecognitionResponse: 识别结果对象。
    """
    try:
        try:
            image_binary = base64.b64decode(request.binary_data)
        except Exception as e:
            return RecognitionResponse(
                code=400,
                info='Base64数据格式无效',
                data=None
            )

        result = request_qwen_vl_with_binary(
            image_binary=image_binary,
            mime_type=request.mime_type,
            prompt=request.prompt,
            additional_prompt=request.additional_prompt
        )

        return RecognitionResponse(
            code=result.get('code', 500),
            info=result.get('info', '识别失败'),
            data=result.get('data')
        )
    except Exception as e:
        return RecognitionResponse(
            code=500,
            info=f'识别过程中发生错误: {str(e)}',
            data=None
        )


def batch_verify_materials_service(request: BatchMaterialVerificationRequest):
    """
    对材料进行批量校验的服务方法。

    Args:
        request (BatchMaterialVerificationRequest): 批量校验请求对象。

    Returns:
        RecognitionResponse: 校验结果对象。
    """
    try:
        # TODO: 实现具体的批量校验逻辑
        # 以下为示例返回，实际需根据业务需求实现
        return RecognitionResponse(
            code=200,
            info="批量校验成功",
            data={"verification_result": "示例结果"}
        )
    except Exception as e:
        return RecognitionResponse(
            code=500,
            info=f"批量校验过程中发生错误: {str(e)}",
            data=None
        )