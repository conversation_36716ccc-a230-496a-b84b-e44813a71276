#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from app.models.chat import TableFormRequest
from app.services.table_service import TableService

logger = logging.getLogger(__name__)

def create_table_router() -> APIRouter:
    """创建表单处理路由"""
    router = APIRouter()
    table_service = TableService()
    
    @router.post("/api/table_form")
    async def process_table_form(request: TableFormRequest):
        """
        处理多模态表单数据（支持图片和视频），返回JSON格式结果
        """
        try:
            result = table_service.process_form_data(
                messages=request.messages,
                form_template=request.form_template
            )
            
            # 根据结果状态码返回相应的HTTP状态码
            if result.get("code") == 404:
                return JSONResponse(result, status_code=404)
            else:
                return JSONResponse(result)
                
        except Exception as e:
            import traceback
            error_msg = f"{str(e)}\n{traceback.format_exc()}"
            logger.error(f"处理表单请求时出错: {error_msg}")
            raise HTTPException(status_code=500, detail=f"处理表单数据时出错: {str(e)}")
    
    return router
