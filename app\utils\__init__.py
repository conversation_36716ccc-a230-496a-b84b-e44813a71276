#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工具模块
"""

from .file_utils import save_temp_file, cleanup_temp_files
from .validator import (
    validate_application_info,
    validate_material_list,
    validate_xform_list,
    validate_newborn_certificate_data,
    validate_image_url,
    validate_base64_data
)
from .qwen_vl_utils import (
    encode_image_to_base64,
    encode_binary_to_base64,
    get_image_mime_type,
    build_qwen_vl_payload,
    get_default_prompt,
    request_qwen_vl,
    request_qwen_vl_with_binary
)

__all__ = [
    # 文件工具
    "save_temp_file",
    "cleanup_temp_files",
    # 验证工具
    "validate_application_info",
    "validate_material_list",
    "validate_xform_list",
    "validate_newborn_certificate_data",
    "validate_image_url",
    "validate_base64_data",
    # Qwen-VL工具
    "encode_image_to_base64",
    "encode_binary_to_base64",
    "get_image_mime_type",
    "build_qwen_vl_payload",
    "get_default_prompt",
    "request_qwen_vl",
    "request_qwen_vl_with_binary"
]
