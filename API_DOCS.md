# API 接口文档

## 基础信息

- **基础URL**: `http://localhost:9870`
- **协议**: HTTP/1.1, WebSocket
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

当前版本暂无认证机制，生产环境建议添加 API Key 认证。

## 通用响应格式

### 成功响应
```json
{
    "status": "success",
    "data": {},
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "status": "error",
    "error": "错误类型",
    "message": "错误描述",
    "details": {}
}
```

## WebSocket 接口

### 聊天连接

**端点**: `ws://localhost:9870/ws/chat`

**协议**: WebSocket

#### 连接初始化

**发送消息**:
```json
{
    "wsOrder": "init_client"
}
```

**接收响应**:
```json
{
    "type": "connection_id",
    "id": "unique_connection_id",
    "message": "连接建立成功"
}
```

#### 发送聊天消息

**消息格式**:
```json
{
    "messages": [
        {
            "role": "user",
            "content": "用户消息内容"
        }
    ],
    "voice": "Cherry",
    "audio_format": "wav",
    "message_type": "chat",
    "stream": true
}
```

**参数说明**:
- `messages`: 消息数组，支持多轮对话
- `voice`: 语音合成声音 (可选)
- `audio_format`: 音频格式 (可选，默认 "wav")
- `message_type`: 消息类型 (默认 "chat")
- `stream`: 是否流式响应 (默认 true)

#### 多模态消息

**文本+图片**:
```json
{
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "请描述这张图片"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
                    }
                }
            ]
        }
    ]
}
```

**文本+音频**:
```json
{
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "请转录这段音频"
                },
                {
                    "type": "audio_url",
                    "audio_url": {
                        "url": "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEA..."
                    }
                }
            ]
        }
    ]
}
```

#### 接收响应

**流式文本响应**:
```json
{
    "type": "text_chunk",
    "content": "部分响应内容",
    "is_final": false
}
```

**音频响应**:
```json
{
    "type": "audio_chunk",
    "audio_data": "base64_encoded_audio",
    "format": "wav"
}
```

**完成响应**:
```json
{
    "type": "completion",
    "message": "响应完成"
}
```

## HTTP 接口

### 1. 主页

**GET** `/`

**响应**:
```json
{
    "message": "千问多模态流式API服务",
    "version": "1.0.0",
    "status": "running"
}
```

### 2. 聊天完成 (流式)

**POST** `/chat/completions/stream`

**请求体**:
```json
{
    "messages": [
        {
            "role": "user",
            "content": "你好"
        }
    ],
    "stream": true,
    "voice": "Cherry",
    "audio_format": "wav"
}
```

**响应**: Server-Sent Events (SSE) 流

### 3. HTTP转WebSocket消息

**POST** `/api/chat_to_websocket`

**请求体**:
```json
{
    "connection_id": "websocket_connection_id",
    "message": "要发送的消息"
}
```

**响应**:
```json
{
    "status": "success",
    "message": "消息已发送"
}
```

### 4. OCR 图像识别

**POST** `/ocr/imagedet`

**Content-Type**: `multipart/form-data` 或 `application/x-www-form-urlencoded`

#### 文件上传方式

**表单参数**:
- `image`: 图片文件 (multipart/form-data)
- `name`: 姓名 (可选)
- `dataType`: 数据类型 (如 "idCard")

#### Base64方式

**表单参数**:
- `image_base64`: Base64编码的图片数据
- `name`: 姓名 (可选)
- `dataType`: 数据类型 (如 "idCard")

**响应**:
```json
{
    "status": "success",
    "data": {
        "name": "张三",
        "id_number": "123456789012345678",
        "address": "北京市朝阳区...",
        "issue_authority": "北京市公安局",
        "valid_period": "2020.01.01-2030.01.01"
    }
}
```

### 5. 表单数据处理

**POST** `/api/table_form`

**请求体**:
```json
{
    "image_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "prompt": "请提取表格中的数据"
}
```

**响应**:
```json
{
    "status": "success",
    "data": {
        "extracted_data": [
            {
                "field": "姓名",
                "value": "张三"
            },
            {
                "field": "年龄",
                "value": "25"
            }
        ]
    }
}
```

### 6. 连接管理

#### 获取活跃连接

**GET** `/api/active_connections`

**响应**:
```json
{
    "status": "success",
    "count": 5,
    "timeout_setting": "900秒",
    "connections": [
        {
            "id": "conn_123",
            "created_at": "2025-07-04T10:30:00Z",
            "last_activity": "2025-07-04T10:35:00Z",
            "is_active": true
        }
    ]
}
```

#### 清理不活跃连接

**POST** `/api/cleanup_connections`

**响应**:
```json
{
    "status": "success",
    "cleaned_count": 3,
    "remaining_count": 2,
    "message": "已清理3个不活跃连接"
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | WebSocket连接不存在 |
| 1002 | 图片格式不支持 |
| 1003 | AI服务调用失败 |
| 1004 | 音频处理失败 |
| 1005 | 视频转换失败 |

## 新生儿办证接口

### 检查新生儿办证

**接口地址**: `POST /newborn/check_certificate`

**功能描述**: 检查新生儿办证申请数据的完整性和有效性

**请求参数**:
```json
{
  "applicationInfo": {
    "businessId": "BIZ001",
    "sessionId": "SESSION001",
    "itemName": "新生儿出生证明申请",
    "itemType": "证照办理",
    "verificationType": 2
  },
  "materialList": [
    {
      "materialCode": "MAT001",
      "materialTypeName": "身份证",
      "fileList": [
        {
          "fileName": "身份证正面.jpg",
          "fileCode": "FILE001",
          "downloadUrl": "https://example.com/idcard_front.jpg"
        }
      ]
    }
  ],
  "xformList": [
    {
      "xformCode": "FORM001",
      "xformName": "新生儿信息表",
      "xformData": {
        "babyName": "张小宝",
        "birthDate": "2024-01-01",
        "gender": "男"
      }
    }
  ]
}
```

**响应示例**:
```json
"新生儿办证检查完成，所有必要信息已验证"
```

**错误响应**:
```json
{
  "error_code": 400,
  "error_message": "缺失必要字段: applicationInfo.businessId"
}
```

### 详细检查新生儿办证

**接口地址**: `POST /newborn/check_certificate_detailed`

**功能描述**: 检查新生儿办证申请数据并返回详细响应

**请求参数**: 同上

**响应示例**:
```json
{
  "success": true,
  "message": "办证检查完成",
  "data": "新生儿办证检查完成，所有必要信息已验证"
}
```

### 健康检查

**接口地址**: `GET /newborn/health`

**响应示例**:
```json
{
  "status": "healthy",
  "service": "newborn_certificate_service",
  "version": "1.0.0"
}
```

## 证照识别接口

### 通过URL识别证照

**接口地址**: `POST /recognition/recognize_by_url`

**功能描述**: 通过图片URL识别证照内容

**请求参数**:
```json
{
  "image_path": "https://example.com/certificate.jpg",
  "prompt": "识别这张图片中的文字信息"
}
```

**响应示例**:
```json
{
  "code": 200,
  "info": "识别成功",
  "data": {
    "文件类型": "身份证",
    "数据": {
      "姓名": "张三",
      "身份证号": "110101199001011234"
    },
    "盖章": ["无"]
  }
}
```

### 通过文件上传识别证照

**接口地址**: `POST /recognition/recognize_by_binary`

**功能描述**: 通过上传文件识别证照内容

**请求参数**:
- `binary_data`: 上传的图片文件 (multipart/form-data)
- `prompt`: 识别提示词 (可选)

**响应示例**: 同上

### 通过Base64识别证照

**接口地址**: `POST /recognition/recognize_by_base64`

**功能描述**: 通过Base64编码的图片数据识别证照内容

**请求参数**:
```json
{
  "binary_data": "base64编码的图片数据",
  "prompt": "识别这张图片中的文字信息",
  "mime_type": "image/jpeg"
}
```

**响应示例**: 同上

### 健康检查

**接口地址**: `GET /recognition/health`

**响应示例**:
```json
{
  "status": "healthy",
  "service": "recognition_service",
  "version": "1.0.0"
}
```


