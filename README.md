# 千问多模态流式API服务

一个基于 FastAPI 的多模态 AI 服务，支持实时音频、文字流式交互，OCR 图像识别和表单数据处理。

## 🚀 功能特性

- **多模态AI聊天**：支持文本、音频、视频输入，实时流式响应
- **WebSocket实时通信**：支持实时双向通信，自动连接管理和清理
- **OCR图像识别**：支持身份证等证件的智能识别
- **表单数据处理**：智能提取图像中的结构化数据
- **音频处理**：支持多种音频格式的生成和处理
- **视频格式转换**：自动转换视频格式以适配AI模型

## 📁 项目结构

```
dmt/
├── app/                          # 主应用包
│   ├── config/                  # 配置模块
│   │   ├── settings.py          # 应用配置和日志设置
│   │   └── prompts.py           # AI提示词配置
│   ├── models/                  # 数据模型
│   │   └── chat.py              # Pydantic数据模型
│   ├── services/                # 业务服务层
│   │   ├── websocket_manager.py # WebSocket连接管理
│   │   ├── ai_service.py        # AI聊天服务
│   │   ├── ocr_service.py       # OCR识别服务
│   │   └── table_service.py     # 表单处理服务
│   ├── routes/                  # 路由处理器
│   │   ├── websocket.py         # WebSocket路由
│   │   ├── chat.py              # 聊天相关路由
│   │   ├── ocr.py               # OCR相关路由
│   │   ├── table.py             # 表单处理路由
│   │   └── management.py        # 连接管理路由
│   └── utils/                   # 工具函数
│       ├── file_utils.py        # 文件处理工具
│       └── prompt_utils.py      # 提示词处理工具
├── main.py                      # 主入口文件
├── temp/                        # 临时文件目录
├── static/                      # 静态文件目录
└── README.md                    # 项目文档
```

## 🛠️ 安装和运行

### 环境要求

- Python 3.8+
- FastAPI
- OpenAI SDK
- 其他依赖见 requirements.txt

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动服务

```bash
python main.py
```

服务将在 `http://0.0.0.0:9870` 启动

## 🔧 配置

### 环境变量

主要配置在 `app/config/settings.py` 中：

- `OPENAI_API_KEY`: OpenAI API 密钥
- `OPENAI_BASE_URL`: API 基础URL（默认使用阿里云DashScope）
- `WEBSOCKET_INACTIVE_TIMEOUT`: WebSocket 连接超时时间（秒）

### AI 提示词

AI 提示词配置在 `app/config/prompts.py` 中，包括：
- `DEFAULT_PROMPT`: 默认聊天提示词
- `COPY_PROMPT`: 复读模式提示词
- `TABLE_PROMPT`: 表单处理提示词

## 📚 API 文档

详细的 API 文档请参考 [API_DOCS.md](API_DOCS.md)

### 主要端点

- `GET /`: 主页
- `WebSocket /ws/chat`: WebSocket 聊天连接
- `POST /api/chat_to_websocket`: HTTP 转 WebSocket 消息
- `POST /chat/completions/stream`: 流式聊天完成
- `POST /api/table_form`: 表单数据处理
- `POST /ocr/imagedet`: OCR 图像识别
- `GET /api/active_connections`: 获取活跃连接
- `POST /api/cleanup_connections`: 清理不活跃连接

## 🔌 WebSocket 使用示例

### 连接初始化

```javascript
const ws = new WebSocket('ws://localhost:9870/ws/chat');

// 发送初始化消息
ws.onopen = function() {
    ws.send(JSON.stringify({wsOrder: "init_client"}));
};

// 接收连接ID
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'connection_id') {
        console.log('连接ID:', data.id);
    }
};
```

### 发送聊天消息

```javascript
const chatMessage = {
    messages: [
        {
            role: "user",
            content: "你好，请介绍一下自己"
        }
    ],
    voice: "Cherry",
    audio_format: "wav",
    message_type: "chat"
};

ws.send(JSON.stringify(chatMessage));
```

## 🖼️ OCR 使用示例

### 文件上传方式

```python
import requests

files = {'image': open('id_card.jpg', 'rb')}
data = {
    'name': '张三',
    'dataType': 'idCard'
}

response = requests.post(
    'http://localhost:9870/ocr/imagedet',
    files=files,
    data=data
)
```

### Base64 方式

```python
import base64
import requests

with open('id_card.jpg', 'rb') as f:
    image_base64 = base64.b64encode(f.read()).decode()

data = {
    'name': '张三',
    'dataType': 'idCard',
    'image_base64': f'data:image/jpeg;base64,{image_base64}'
}

response = requests.post(
    'http://localhost:9870/ocr/imagedet',
    data=data
)
```

## 🏗️ 架构设计

### 分层架构

1. **路由层 (Routes)**：处理HTTP请求和WebSocket连接
2. **服务层 (Services)**：业务逻辑处理
3. **模型层 (Models)**：数据模型定义
4. **配置层 (Config)**：应用配置管理
5. **工具层 (Utils)**：通用工具函数

### 设计原则

- **单一职责**：每个模块只负责一个功能领域
- **依赖注入**：服务之间通过依赖注入解耦
- **配置外部化**：所有配置项集中管理
- **错误处理**：统一的错误处理和日志记录

## 🧪 测试

### 运行测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio

# 运行测试
pytest tests/
```

### 测试覆盖率

```bash
pip install pytest-cov
pytest --cov=app tests/
```

## 📝 开发指南

### 添加新功能

1. 在 `app/models/` 中定义数据模型
2. 在 `app/services/` 中实现业务逻辑
3. 在 `app/routes/` 中添加路由处理
4. 在 `main.py` 中注册新路由

### 代码规范

- 使用 Black 进行代码格式化
- 使用 isort 进行导入排序
- 遵循 PEP 8 编码规范
- 添加类型注解

## 🚨 注意事项

1. **安全性**：生产环境请修改默认的 API 密钥
2. **性能**：大文件处理时注意内存使用
3. **日志**：定期清理日志文件
4. **备份**：重要数据请及时备份

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题，请联系开发团队。
