#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging

# 应用配置
APP_TITLE = "千问多模态流式API"
APP_HOST = "0.0.0.0"
APP_PORT = 9870

# OpenAI客户端配置
OPENAI_API_KEY = 'sk-d4a8b47f7c8440a6a832dd7d370add3c'
OPENAI_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
OPENAI_MODEL = "qwen-omni-turbo"

# WebSocket配置
WEBSOCKET_INACTIVE_TIMEOUT = 900  # 15分钟超时，单位为秒
WEBSOCKET_CLEANUP_INTERVAL = 300  # 每5分钟检查一次

# 文件配置
TEMP_DIR = "temp"
STATIC_DIR = "static"
TEMP_FILE_CLEANUP_DELAY = 300  # 5分钟后删除临时文件

# OCR服务配置
OCR_API_KEY = "app-iJQxefmfrzSpx5ElJlBhP6Ax"
OCR_UPLOAD_URL = "http://*************/v1/files/upload"
OCR_WORKFLOW_URL = "http://*************/v1/workflows/run"

# 音频配置
DEFAULT_VOICE = "Cherry"
DEFAULT_AUDIO_FORMAT = "wav"
AUDIO_SAMPLE_RATE = 24000

# 新生儿办证配置
NEWBORN_SERVICE_ENABLED = True
NEWBORN_SERVICE_VERSION = "1.0.0"

# 证照识别配置
RECOGNITION_SERVICE_ENABLED = True
RECOGNITION_DEFAULT_PROMPT = ('仔细观察这张图片，然后json结构化输出图片中的所有字段信息和图片的类型，'
                             '输出包含文件类型、数据（除文件类型和盖章外的其他文字属性）、盖章（list）。'
                             '文件的类型证照的标题，名字为文件类型。检查图片上是否有盖章，属性为盖章，'
                             '值为盖章上的单位名称；若无，值为无。如无法识别有效的OCR信息，'
                             '文件类型输出为不是合法的文件类型，其他值为空')

# Qwen-VL模型配置
QWEN_VL_MODEL = "qwen-vl-max-latest"
QWEN_VL_SYSTEM_PROMPT = "你是一个识别电子证照的助手，不会输出图中没有出现的文字信息，不会输出水印"

# 日志配置
def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO, 
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

# CORS配置
CORS_ORIGINS = ["*"]  # 在生产环境中应该设置为特定的域名
CORS_CREDENTIALS = True
CORS_METHODS = ["*"]
CORS_HEADERS = ["*"]
