#!/usr/bin/env python
# -*- coding: utf-8 -*- 

"""
证照识别相关路由
"""

from fastapi import APIRouter, HTTPException, File, UploadFile, Form
from app.models.recognition_models import (RecognitionResponse, 
                                         RecognizeLicenseByBinaryRequest, 
                                         RecognizeLicenseByUrlRequest, 
                                         BatchMaterialVerificationRequest)  # 更新导入
from app.services.recognition_service import (recognize_license_by_url_service, 
                                             recognize_license_by_binary_service, 
                                             recognize_license_by_base64_service, 
                                             batch_verify_materials_service)  # 假设新服务函数

router = APIRouter(prefix="/recognition", tags=["证照识别"])


@router.post('/recognize_by_url', response_model=RecognitionResponse)
def recognize_license_by_url(
    request: RecognizeLicenseByUrlRequest
) -> RecognitionResponse:
    """
    通过图片 URL 识别证照的路由方法。

    Args:
        request (RecognizeLicenseByUrlRequest): 包含图片 URL、提示词和附加提示词的请求对象。

    Returns:
        RecognitionResponse: 识别结果对象。

    Raises:
        HTTPException: 当 image_url 格式无效或识别过程中发生错误时抛出。
    """
    try:
        return recognize_license_by_url_service(request)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post('/recognize_by_binary', response_model=RecognitionResponse)
def recognize_license_by_binary(
    binary_data: UploadFile = File(...,describe="图片文件"),
    prompt: str = Form('',description="提示词"),
    additional_prompt: str = Form('',description="附加提示词")
) -> RecognitionResponse:
    """
    通过二进制图片数据识别证照的路由方法。

    Args:
        binary_data (UploadFile): 图片文件对象。
        prompt (str): 提示词。
        additional_prompt (str): 附加提示词。

    Returns:
        RecognitionResponse: 识别结果对象。
    """
    return recognize_license_by_binary_service(binary_data, prompt, additional_prompt)


@router.post('/recognize_by_base64', response_model=RecognitionResponse)
def recognize_license_by_base64(request: RecognizeLicenseByBinaryRequest) -> RecognitionResponse:
    """
    通过 Base64 编码的图片数据识别证照的路由方法。

    Args:
        request (RecognizeLicenseByBinaryRequest): 包含 Base64 数据、提示词和附加提示词的请求对象。

    Returns:
        RecognitionResponse: 识别结果对象。
    """
    return recognize_license_by_base64_service(request)


@router.post('/batch_verify', response_model=RecognitionResponse)
def batch_verify_materials(
    request: BatchMaterialVerificationRequest
) -> RecognitionResponse:
    """
    对材料进行批量校验的路由方法。

    Args:
        request (BatchMaterialVerificationRequest): 批量校验请求对象。

    Returns:
        RecognitionResponse: 校验结果对象。

    Raises:
        HTTPException: 当请求参数无效或校验过程中发生错误时抛出。
    """
    try:
        return batch_verify_materials_service(request)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
def health_check():
    """
    健康检查接口
    
    Returns:
        Dict: 服务状态信息
    """
    return {
        "status": "healthy",
        "service": "recognition_routes",
        "version": "1.0.0"
    }
