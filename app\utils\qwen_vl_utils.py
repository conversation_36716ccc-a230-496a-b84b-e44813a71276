#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qwen-VL 模型请求工具模块，用于处理与 Qwen-VL 模型的交互
"""

import base64
import json
import os
import requests
from typing import Dict, Optional
from app.config.settings import OPENAI_API_KEY, OPENAI_BASE_URL


def encode_image_to_base64(image_path: str) -> str:
    """
    将图片文件编码为 Base64 字符串
    
    Args:
        image_path: 图片文件路径
        
    Returns:
        str: Base64编码的图片数据
    """
    with open(image_path, 'rb') as f:
        return base64.b64encode(f.read()).decode('utf-8')


def encode_binary_to_base64(image_binary: bytes) -> str:
    """
    将图片二进制流编码为 Base64 字符串
    
    Args:
        image_binary: 图片二进制数据
        
    Returns:
        str: Base64编码的图片数据
    """
    return base64.b64encode(image_binary).decode('utf-8')


def get_image_mime_type(image_path: str = None) -> str:
    """
    根据文件扩展名获取图片的 MIME 类型
    
    Args:
        image_path: 图片文件路径（可选）
        
    Returns:
        str: 图片的MIME类型
    """
    if image_path:
        ext = os.path.splitext(image_path)[1].lower()
        mime_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp'
        }
        return mime_types.get(ext, 'image/jpeg')
    return 'image/jpeg'


def build_qwen_vl_payload(mime_type: str, image_base64: str, prompt: str) -> Dict:
    """
    构建 Qwen-VL 模型请求的 payload
    
    Args:
        mime_type: 图片MIME类型
        image_base64: Base64编码的图片数据
        prompt: 文本提示词
        
    Returns:
        Dict: 请求payload
    """
    return {
        "model": "qwen-vl-max-latest",
        "messages": [
            {
                "role": "system",
                "content": [
                    {"type": "text", "text": "你是一个识别电子证照的助手，不会输出图中没有出现的文字信息，不会输出水印"}
                ]
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:{mime_type};base64,{image_base64}"}}
                ]
            }
        ]
    }


def get_default_prompt() -> str:
    """
    获取默认的识别提示词
    
    Returns:
        str: 默认提示词
    """
    return ('仔细观察这张图片，然后json结构化输出图片中的所有字段信息和图片的类型，'
            '输出包含文件类型、数据（除文件类型和盖章外的其他文字属性）、盖章（list）。'
            '文件的类型证照的标题，名字为文件类型。检查图片上是否有盖章，属性为盖章，'
            '值为盖章上的单位名称；若无，值为无。如无法识别有效的OCR信息，'
            '文件类型输出为不是合法的文件类型，其他值为空')


def request_qwen_vl(image_path: str, prompt: str = None, additional_prompt: str = None) -> Dict:
    """
    使用图片路径或URL请求 Qwen-VL 模型

    Args:
        image_path: 图片文件路径或URL
        prompt: 文本提示词（可选）
        additional_prompt: 附加提示词（可选）

    Returns:
        Dict: 包含状态码、信息和数据的响应
    """
    default_prompt = get_default_prompt()
    prompt = default_prompt if prompt is None or prompt == '' else prompt
    if additional_prompt and additional_prompt.strip(): 
        prompt = prompt + ' ' + additional_prompt.strip()

    try:
        if image_path.startswith(('http://', 'https://')):
            # 如果是 URL，直接使用 requests 获取图片内容
            response = requests.get(image_path)
            response.raise_for_status()
            image_base64 = encode_binary_to_base64(response.content)
            mime_type = response.headers.get('Content-Type', 'image/jpeg')
        else:
            image_base64 = encode_image_to_base64(image_path)
            mime_type = get_image_mime_type(image_path)

        payload = build_qwen_vl_payload(mime_type, image_base64, prompt)
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {OPENAI_API_KEY}"
        }

        api_url = f"{OPENAI_BASE_URL}/chat/completions"
        response = requests.post(api_url, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()

        if ('choices' in result and result['choices'] and 
            'message' in result['choices'][0] and 
            'content' in result['choices'][0]['message']):

            content = result['choices'][0]['message']['content']
            try:
                # 去除可能存在的 ```json 和 ``` 标记
                content = content.replace('```json', '').replace('```', '').strip()
                formatted_data = json.loads(content)
                return {"code": 200, "info": "识别成功", "data": formatted_data}
            except json.JSONDecodeError:
                return {"code": 500, "info": "无法识别，结果非有效 JSON 格式", "data": None}
        else:
            return {"code": 500, "info": "无法识别", "data": None}

    except Exception as e:
        print(f"请求错误: {e}")
        return {"code": 500, "info": "无法识别", "data": None}


def request_qwen_vl_with_binary(image_binary: bytes, mime_type: str = 'image/jpeg', prompt: str = None, additional_prompt: str = None) -> Dict:
    """
    使用图片二进制流请求 Qwen-VL 模型

    Args:
        image_binary: 图片二进制流
        mime_type: 图片的 MIME 类型，默认为 image/jpeg
        prompt: 文本提示，默认为指定格式的识别要求
        additional_prompt: 附加提示词（可选）

    Returns:
        Dict: 包含状态码、信息和数据的 JSON 响应
    """
    default_prompt = get_default_prompt()
    prompt = default_prompt if prompt is None or prompt == '' else prompt
    if additional_prompt and additional_prompt.strip(): 
        prompt = prompt + ' ' + additional_prompt.strip()

    try:
        # 简单验证二进制流是否为空
        if not image_binary:
            return {"code": 500, "info": "流错误", "data": None}

        image_base64 = encode_binary_to_base64(image_binary)
        payload = build_qwen_vl_payload(mime_type, image_base64, prompt)
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {OPENAI_API_KEY}"
        }

        api_url = f"{OPENAI_BASE_URL}/chat/completions"
        response = requests.post(api_url, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()

        if ('choices' in result and result['choices'] and
            'message' in result['choices'][0] and
            'content' in result['choices'][0]['message']):

            content = result['choices'][0]['message']['content']
            try:
                # 打印原始内容，方便调试
                print(f"原始响应内容: {content}")
                # 去除可能存在的 ```json 和 ``` 标记
                content = content.replace('```json', '').replace('```', '').strip()
                # 进一步清理，去除前后可能存在的非 JSON 字符
                # 找到第一个 '{' 和最后一个 '}' 的位置
                start_index = content.find('{')
                end_index = content.rfind('}')
                if start_index != -1 and end_index != -1:
                    content = content[start_index:end_index + 1]
                formatted_data = json.loads(content)
                return {"code": 200, "info": "识别成功", "data": formatted_data}
            except json.JSONDecodeError as e:
                print(f"JSON 解析错误: {e}")
                return {"code": 500, "info": "无法识别，结果非有效 JSON 格式", "data": None}
        else:
            return {"code": 500, "info": "无法识别", "data": None}
    except Exception as e:
        print(f"请求错误: {e}")
        return {"code": 500, "info": "流错误", "data": None}
