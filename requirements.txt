# Web框架和服务器
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# AI和机器学习
openai>=1.3.0

# 音频处理
soundfile>=0.12.1
numpy>=1.24.0

# HTTP客户端
httpx>=0.25.0
requests>=2.31.0

# 数据验证和序列化
pydantic>=2.4.0

# 异步支持
asyncio-mqtt>=0.13.0

# 文件处理
aiofiles>=23.2.1

# 日志和配置
python-dotenv>=1.0.0
PyYAML>=6.0.0

# 类型提示
typing-extensions>=4.8.0

# 时间处理
python-dateutil>=2.8.2

# JSON处理
orjson>=3.9.0

# WebSocket支持 (已包含在uvicorn[standard]中)
websockets>=11.0.3

# CORS支持 (已包含在FastAPI中)
# fastapi已包含starlette，其中包含CORS中间件

# 可选：视频处理 (如果需要)
# opencv-python>=4.8.0
# ffmpeg-python>=0.2.0

# 可选：图像处理
# Pillow>=10.0.0

# 可选：加密和安全
# cryptography>=41.0.0
# passlib[bcrypt]>=1.7.4

# 可选：数据库支持
# sqlalchemy>=2.0.0
# alembic>=1.12.0

# 可选：缓存支持
# redis>=5.0.0
# aioredis>=2.0.0

# 可选：监控和指标
# prometheus-client>=0.17.0

# 可选：限流
# slowapi>=0.1.9
