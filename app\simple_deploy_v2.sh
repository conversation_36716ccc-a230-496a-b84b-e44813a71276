#!/bin/bash

# 简化版部署脚本 - 基于成功的 start_server.sh 和 stop_server.sh 方法
# 使用方法: ./simple_deploy_v2.sh [start|stop|restart|status|logs]

set -e

# 配置变量
PROJECT_NAME="dmt"
SERVICE_PORT="9870"
WORKERS=1
PID_FILE="dmt.pid"
LOG_DIR="./logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/dmt_$TIMESTAMP.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    if [ ! -d "dmt" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv dmt
    fi
    
    log_info "安装/更新依赖..."
    dmt/bin/pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple
    dmt/bin/pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
}

# 检查进程是否运行 (完全参考 start_server.sh 的方法)
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat $PID_FILE)
        if ps -p $pid > /dev/null 2>&1; then
            return 0
        else
            log_warn "发现过期的PID文件，将删除"
            rm -f $PID_FILE
            return 1
        fi
    fi
    return 1
}

# 启动服务 (参考 start_server.sh 的方法)
start() {
    if is_running; then
        local pid=$(cat $PID_FILE)
        log_warn "服务器已经在运行中，PID: $pid"
        return 0
    fi
    
    log_info "检查Python环境..."
    check_python
    
    # 设置日志目录
    mkdir -p $LOG_DIR
    
    # 启动服务器
    log_info "正在启动DMT服务器，日志将保存到 $LOG_FILE"
    nohup dmt/bin/uvicorn main:app \
        --host 0.0.0.0 \
        --port $SERVICE_PORT \
        --workers $WORKERS \
        --log-level info \
        > "$LOG_FILE" 2>&1 &
    
    # 保存PID
    echo $! > $PID_FILE
    log_info "服务器已启动，PID: $!"
    log_info "服务地址: http://localhost:$SERVICE_PORT"
    log_info "健康检查: http://localhost:$SERVICE_PORT/newborn/health"
    log_info "使用 $0 stop 停止服务器"
    log_info "使用 tail -f $LOG_FILE 查看实时日志"
    
    # 等待服务启动
    sleep 3
    
    # 验证服务是否成功启动
    if is_running; then
        log_info "服务启动验证: 成功"
    else
        log_error "服务启动验证: 失败"
        return 1
    fi
}

# 停止服务 (完全参考 stop_server.sh 的方法)
stop() {
    # 检查PID文件是否存在
    if [ ! -f "$PID_FILE" ]; then
        log_warn "未找到PID文件，服务器可能未运行"
        return 0
    fi
    
    # 读取PID
    local pid=$(cat $PID_FILE)
    
    # 检查进程是否存在
    if ! ps -p $pid > /dev/null 2>&1; then
        log_warn "进程 $pid 不存在，可能已经停止"
        rm -f $PID_FILE
        return 0
    fi
    
    log_info "正在停止DMT服务器 (PID: $pid)..."
    
    # 先尝试优雅停止
    kill $pid
    log_info "已发送停止信号，等待服务器关闭..."
    
    # 等待进程终止，最多等待10秒
    local count=0
    while [ $count -lt 10 ]; do
        if ! ps -p $pid > /dev/null 2>&1; then
            log_info "服务器已成功停止"
            rm -f $PID_FILE
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    # 如果进程仍然存在，强制终止
    log_warn "服务器未能在预期时间内停止，正在强制终止..."
    kill -9 $pid
    
    # 再次检查进程
    if ! ps -p $pid > /dev/null 2>&1; then
        log_info "服务器已被强制停止"
        rm -f $PID_FILE
        return 0
    else
        log_error "无法停止服务器，请手动检查进程 $pid"
        return 1
    fi
}

# 重启服务
restart() {
    log_info "重启DMT服务..."
    stop
    sleep 2
    start
}

# 查看状态
status() {
    echo "=== DMT服务状态 ==="
    if is_running; then
        local pid=$(cat $PID_FILE)
        echo "状态: 运行中"
        echo "PID: $pid"
        echo "端口: $SERVICE_PORT"
        
        # 检查端口监听
        if netstat -tlnp 2>/dev/null | grep -q ":$SERVICE_PORT "; then
            echo "端口监听: 正常"
        else
            echo "端口监听: 异常"
        fi
        
        # 检查进程资源使用
        if command -v ps &> /dev/null; then
            echo "资源使用:"
            ps -p $pid -o pid,ppid,pcpu,pmem,etime,cmd --no-headers 2>/dev/null || true
        fi
        
        # 健康检查
        if command -v curl &> /dev/null; then
            echo ""
            echo "=== 健康检查 ==="
            if curl -s -f "http://localhost:$SERVICE_PORT/newborn/health" > /dev/null 2>&1; then
                echo "健康检查: 通过"
            else
                echo "健康检查: 失败"
            fi
        fi
    else
        echo "状态: 未运行"
    fi
    
    echo ""
    echo "=== 最近日志 ==="
    # 查找最新的日志文件
    local latest_log=$(ls -t $LOG_DIR/dmt_*.log 2>/dev/null | head -n 1)
    if [ -n "$latest_log" ]; then
        echo "日志文件: $latest_log"
        tail -n 10 "$latest_log"
    else
        echo "日志文件不存在"
    fi
}

# 查看日志
logs() {
    # 查找最新的日志文件
    local latest_log=$(ls -t $LOG_DIR/dmt_*.log 2>/dev/null | head -n 1)
    if [ -n "$latest_log" ]; then
        log_info "显示日志文件: $latest_log"
        tail -f "$latest_log"
    else
        log_error "日志文件不存在"
        return 1
    fi
}

# 强制清理 (额外功能)
force_clean() {
    log_warn "强制清理所有相关进程..."
    
    # 杀死所有uvicorn进程
    pkill -f "uvicorn.*main:app" 2>/dev/null || true
    
    # 杀死占用端口的进程
    local port_pids=$(lsof -ti:$SERVICE_PORT 2>/dev/null || echo "")
    if [ -n "$port_pids" ]; then
        kill -9 $port_pids 2>/dev/null || true
    fi
    
    # 清理PID文件
    rm -f $PID_FILE
    
    log_info "清理完成"
}

# 主函数
main() {
    case "${1:-}" in
        start)
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        status)
            status
            ;;
        logs)
            logs
            ;;
        force-clean)
            force_clean
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|status|logs|force-clean}"
            echo ""
            echo "  start       - 启动服务"
            echo "  stop        - 停止服务"
            echo "  restart     - 重启服务"
            echo "  status      - 查看服务状态"
            echo "  logs        - 查看实时日志"
            echo "  force-clean - 强制清理所有相关进程"
            echo ""
            echo "示例:"
            echo "  $0 start        # 启动服务"
            echo "  $0 status       # 查看状态"
            echo "  $0 logs         # 查看日志"
            echo "  $0 stop         # 停止服务"
            echo "  $0 force-clean  # 强制清理"
            exit 1
            ;;
    esac
}

main "$@"
