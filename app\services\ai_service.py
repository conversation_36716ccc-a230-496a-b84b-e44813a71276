#!/usr/bin/env python
# -*- coding: utf-8 -*-

import base64
import numpy as np
import soundfile as sf
import time
import logging
from openai import OpenAI
from typing import List, Dict, Any, Optional

from app.config.settings import (
    OPENAI_API_KEY, OPENAI_BASE_URL, OPENAI_MODEL,
    AUDIO_SAMPLE_RATE, TEMP_DIR
)
from app.utils.file_utils import cleanup_temp_files

logger = logging.getLogger(__name__)

class AIService:
    """AI服务类，处理与千问模型的交互"""

    def __init__(self):
        self.client = OpenAI(
            api_key=OPENAI_API_KEY,
            base_url=OPENAI_BASE_URL,
        )

    def create_completion(self, messages: List[Dict[str, Any]],
                         modalities: List[str] = ["text", "audio"],
                         voice: str = "Cherry",
                         audio_format: str = "wav",
                         stream: bool = True):
        """
        创建AI完成请求
        """
        return self.client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=messages,
            modalities=modalities,
            audio={"voice": voice, "format": audio_format} if "audio" in modalities else None,
            stream=stream,
            stream_options={"include_usage": True} if stream else None,
        )

    def process_video_content(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理视频消息：检查消息内容中是否包含视频数据，并转换格式
        """
        try:
            from video_converter import VideoConverter
        except ImportError:
            logger.warning("video_converter模块未找到，跳过视频处理")
            return messages

        processed_messages = []
        for msg in messages:
            if msg.get("role") == "user" and isinstance(msg.get("content"), list):
                new_content = []
                for content_item in msg["content"]:
                    if content_item.get("type") == "video_url" and "video_url" in content_item:
                        video_data = content_item["video_url"]
                        video_url = video_data.get("url", "")
                        video_format = video_data.get("format", "webm")

                        logger.info(f"检测到视频数据，格式: {video_format}")

                        # 如果不是mp4格式，转换为mp4
                        if video_format != "mp4" and video_url:
                            logger.info(f"开始将视频从 {video_format} 转换为 mp4")
                            mp4_url = VideoConverter.convert_base64_video(video_url, video_format, "mp4")
                            if mp4_url:
                                # 更新视频URL和格式
                                content_item["video_url"]["url"] = mp4_url
                                content_item["video_url"]["format"] = "mp4"
                                logger.info("视频转换成功")
                            else:
                                logger.error("视频转换失败")

                    new_content.append(content_item)

                # 更新消息内容
                processed_msg = msg.copy()
                processed_msg["content"] = new_content
                processed_messages.append(processed_msg)
            else:
                processed_messages.append(msg)

        return processed_messages

    def save_audio_file(self, audio_data: bytes, prefix: str = "audio") -> str:
        """
        保存音频文件并返回文件路径
        """
        audio_path = f"{TEMP_DIR}/{prefix}_{int(time.time())}.wav"
        # 将完整的音频数据转换为numpy数组
        full_audio_np = np.frombuffer(audio_data, dtype=np.int16)
        # 保存为WAV文件
        sf.write(audio_path, full_audio_np, samplerate=AUDIO_SAMPLE_RATE)

        # 安排删除临时文件
        cleanup_temp_files(audio_path)

        return audio_path
