#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import time
import base64
import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse

from app.models.chat import ChatRequest, WebSocketChatRequest, MaterialUploadRequest
from app.services.websocket_manager import WebSocketConnectionManager
from app.services.ai_service import AIService
from app.config.prompts import DEFAULT_PROMPT, COPY_PROMPT
from app.utils.prompt_utils import add_default_prompt

logger = logging.getLogger(__name__)

def create_chat_router(websocket_manager: WebSocketConnectionManager) -> APIRouter:
    """创建聊天路由"""
    router = APIRouter()
    ai_service = AIService()
    
    @router.post("/chat/completions/stream")
    async def stream_chat_completions(request: ChatRequest):
        """流式返回千问多模态模型的音频和文字响应"""
        
        # 转换消息格式
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
        
        # 调用大模型
        completion = ai_service.create_completion(
            messages=messages,
            modalities=["text", "audio"],
            voice=request.voice,
            audio_format=request.audio_format,
            stream=True
        )
        
        async def generate():
            text_response = ""
            full_audio_data = b''
            
            try:
                # 处理流式响应
                for chunk in completion:
                    if chunk.choices:
                        # 处理音频内容
                        if hasattr(chunk.choices[0].delta, "audio"):
                            # 从音频数据中提取
                            if "data" in chunk.choices[0].delta.audio:
                                audio_string = chunk.choices[0].delta.audio["data"]
                                # 解码base64数据
                                wav_bytes = base64.b64decode(audio_string)
                                # 累积完整的音频数据
                                full_audio_data += wav_bytes
                                
                                # 构建响应数据
                                response_data = {
                                    "type": "audio",
                                    "data": audio_string
                                }
                                yield f"data: {json.dumps(response_data)}\n\n"
                            
                            # 处理文字内容
                            if "transcript" in chunk.choices[0].delta.audio:
                                transcript = chunk.choices[0].delta.audio["transcript"]
                                text_response += transcript
                                
                                # 构建响应数据
                                response_data = {
                                    "type": "text",
                                    "data": transcript
                                }
                                yield f"data: {json.dumps(response_data)}\n\n"
                    
                    elif hasattr(chunk, 'usage'):
                        # 发送使用统计
                        response_data = {
                            "type": "usage",
                            "data": str(chunk.usage)
                        }
                        yield f"data: {json.dumps(response_data)}\n\n"
                
                # 保存完整的音频文件（可选）
                if full_audio_data:
                    audio_path = ai_service.save_audio_file(full_audio_data, "streaming_audio")
                    
                    # 发送完成信号
                    response_data = {
                        "type": "complete",
                        "data": {
                            "text": text_response,
                            "audio_path": audio_path
                        }
                    }
                    yield f"data: {json.dumps(response_data)}\n\n"
                    
            except Exception as e:
                import traceback
                error_msg = f"{str(e)}\n{traceback.format_exc()}"
                logger.error(f"错误: {error_msg}")
                # 发送错误信息
                response_data = {
                    "type": "error",
                    "data": str(e)
                }
                yield f"data: {json.dumps(response_data)}\n\n"
        
        return StreamingResponse(generate(), media_type="text/event-stream")
    
    @router.post("/api/chat_to_websocket")
    async def chat_to_websocket(request: WebSocketChatRequest):
        """
        接收HTTP请求并将结果发送到指定的WebSocket连接
        """
        websocket_id = request.websocket_id
        websocket = websocket_manager.get_connection(websocket_id)
        
        receive_time = time.time()
        logger.info(f"收到客户端消息，时间: {datetime.fromtimestamp(receive_time).strftime('%Y-%m-%d %H:%M:%S.%f')}")
        if not websocket:
            raise HTTPException(status_code=404, detail=f"未找到WebSocket连接，ID: {websocket_id}")
        
        # 更新连接活动时间
        websocket_manager.update_activity(websocket_id)
        
        logger.info(f"接收到HTTP请求，将发送消息到WebSocket连接 ID: {websocket_id}")
        
        # 转换消息格式
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
        
        # 根据消息类型选择不同的提示词
        if request.message_type == "bsevent":
            messages_with_prompt = add_default_prompt(DEFAULT_PROMPT, messages)
        elif request.message_type == "fzcopy":
            messages_with_prompt = add_default_prompt(COPY_PROMPT, messages)
        elif request.message_type == "otherReply":
            messages_with_prompt = messages
        else:
            # 默认使用COPY_PROMPT
            messages_with_prompt = add_default_prompt(COPY_PROMPT, messages)
        
        logger.info(f"添加默认提示后的消息:{messages_with_prompt}")
        try:
            # 调用大模型
            completion = ai_service.create_completion(
                messages=messages_with_prompt,
                modalities=["text", "audio"],
                voice=request.voice,
                audio_format=request.audio_format,
                stream=True
            )
            
            # 准备响应数据
            text_response = ""
            full_audio_data = b''
            
            # 处理流式响应
            for chunk in completion:
                # 定期更新连接活动时间
                websocket_manager.update_activity(websocket_id)
                
                if chunk.choices:
                    # 处理音频内容
                    if hasattr(chunk.choices[0].delta, "audio"):
                        # 从音频数据中提取
                        if "data" in chunk.choices[0].delta.audio:
                            audio_string = chunk.choices[0].delta.audio["data"]
                            # 解码base64数据
                            wav_bytes = base64.b64decode(audio_string)
                            # 累积完整的音频数据
                            full_audio_data += wav_bytes
                            
                            # 发送音频数据给客户端
                            await websocket.send_json({"type": "audio", "data": audio_string})
                        
                        # 处理文字内容
                        if "transcript" in chunk.choices[0].delta.audio:
                            transcript = chunk.choices[0].delta.audio["transcript"]
                            text_response += transcript
                            
                            # 发送文字数据给客户端
                            if request.message_type == "otherReply":
                                await websocket.send_json({"type": "aitext", "data": transcript})
                            else:
                                await websocket.send_json({"type": "text", "data": transcript})
                
                elif hasattr(chunk, 'usage'):
                    # 发送使用统计
                    await websocket.send_json({"type": "usage", "data": str(chunk.usage)})
            
            # 保存完整的音频文件（可选）
            if full_audio_data:
                audio_path = ai_service.save_audio_file(full_audio_data)
                
                # 发送完成信号
                if request.message_type == "otherReply":
                    await websocket.send_json({
                        "type": "aicomplete", 
                        "data": {
                            "text": text_response,
                            "audio_path": audio_path
                        }
                    })
                else:
                    await websocket.send_json({
                        "type": "complete", 
                        "data": {
                            "text": text_response,
                            "audio_path": audio_path
                        }
                    })
            
            # 最后更新连接活动时间
            websocket_manager.update_activity(websocket_id)

            complete_time = time.time()
            logger.info(f"处理消息完成，complete时间: {datetime.fromtimestamp(complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')}, 总耗时: {complete_time - receive_time:.3f}秒")
            return JSONResponse({
                "status": "success", 
                "message": "消息已成功发送到WebSocket连接",
                "websocket_id": websocket_id
            })
                
        except Exception as e:
            import traceback
            error_msg = f"{str(e)}\n{traceback.format_exc()}"
            logger.error(f"处理HTTP请求发送消息到WebSocket时出错: {error_msg}")
            
            # 尝试向WebSocket发送错误信息
            try:
                await websocket.send_json({"type": "error", "data": str(e)})
            except:
                logger.error("无法发送错误消息到WebSocket，连接可能已断开")
                
            raise HTTPException(status_code=500, detail=f"处理消息时出错: {str(e)}")
    
    @router.post("/api/forward_to_websocket")
    async def forward_to_websocket(request: MaterialUploadRequest):
        """
        接收数据并将其转发到指定的WebSocket连接
        """
        websocket_id = request.websocket_id
        websocket = websocket_manager.get_connection(websocket_id)
        
        receive_time = time.time()
        logger.info(f"收到材料上传转发请求，时间: {datetime.fromtimestamp(receive_time).strftime('%Y-%m-%d %H:%M:%S.%f')}")
        if not websocket:
            raise HTTPException(status_code=404, detail=f"未找到WebSocket连接，ID: {websocket_id}")
        
        # 更新连接活动时间
        websocket_manager.update_activity(websocket_id)
        
        logger.info(f"接收到材料上传转发请求，将发送数据到WebSocket连接 ID: {websocket_id}")
        
        try:
            # 检查消息类型
            if request.message_type == "materialsup":
                # 直接转发数据到WebSocket
                await websocket.send_json({
                    "type": "materialsup",
                    "data": request.data
                })
                
                logger.info(f"数据已成功转发到WebSocket连接 ID: {websocket_id}")
                return JSONResponse({
                    "status": "success", 
                    "message": "数据已成功转发到WebSocket连接",
                    "websocket_id": websocket_id
                })
            else:
                # 不支持的消息类型
                logger.warning(f"不支持的消息类型: {request.message_type}")
                return JSONResponse({
                    "status": "error",
                    "message": f"不支持的消息类型: {request.message_type}"
                }, status_code=400)
                
        except Exception as e:
            import traceback
            error_msg = f"{str(e)}\n{traceback.format_exc()}"
            logger.error(f"转发数据到WebSocket时出错: {error_msg}")
            
            # 尝试向WebSocket发送错误信息
            try:
                await websocket.send_json({"type": "error", "data": str(e)})
            except:
                logger.error("无法发送错误消息到WebSocket，连接可能已断开")
                
            raise HTTPException(status_code=500, detail=f"转发数据时出错: {str(e)}")
    
    return router
