#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, File, UploadFile, Form
from fastapi.responses import JSONResponse

from app.services.ocr_service import OCRService

logger = logging.getLogger(__name__)

def create_ocr_router() -> APIRouter:
    """创建OCR路由"""
    router = APIRouter()
    ocr_service = OCRService()
    
    @router.post("/ocr/imagedet")
    async def ocr_image_detection(
        name: str = Form(...),
        dataType: str = Form(...),  # idCard表示身份证，sign表示用户签名
        idNumber: Optional[str] = Form(None),  # 修改为可选参数
        image: Optional[UploadFile] = File(None),  # 图片文件，可选
        image_base64: Optional[str] = Form(None, max_length=10485760)  # Base64编码的图片，可选
    ):
        """
        接收图片文件或Base64编码的图片，发送到OCR服务进行识别，并返回结果
        """
        try:
            # 检查输入参数，确保提供了图片
            if image is None and not image_base64:
                raise HTTPException(status_code=400, detail="必须提供图片文件或Base64编码的图片")
            
            # 根据输入的图片格式选择不同的处理方式
            if image:
                logger.info(f"收到OCR图像识别请求，文件名: {image.filename}, 类型: {dataType}")
                # 获取图片内容
                image_data = await image.read()
                
                # 确定图片类型
                content_type = image.content_type
                if not content_type or "image/" not in content_type:
                    # 如果未提供或无效，根据文件扩展名猜测
                    ext = image.filename.split('.')[-1].lower()
                    content_type = f"image/{ext}"
                    if ext not in ["jpeg", "jpg", "png", "webp", "gif"]:
                        content_type = "image/jpeg"  # 默认类型
            else:
                logger.info(f"收到OCR图像识别请求，使用Base64编码图片, 类型: {dataType}")
                
                # 解析Base64字符串
                image_data, content_type = ocr_service.parse_base64_image(image_base64)
            
            # 调用OCR服务处理图像
            result = ocr_service.process_image(
                image_data=image_data,
                content_type=content_type,
                name=name,
                data_type=dataType,
                id_number=idNumber
            )
            
            return JSONResponse(result)
            
        except Exception as e:
            import traceback
            error_msg = f"{str(e)}\n{traceback.format_exc()}"
            logger.error(f"OCR图像处理过程中出错: {error_msg}")
            raise HTTPException(
                status_code=500,
                detail=f"处理图像时出错: {str(e)}"
            )
    
    return router
