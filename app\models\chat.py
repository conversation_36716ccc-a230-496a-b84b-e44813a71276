#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from pydantic import BaseModel

class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str
    content: str

class ChatRequest(BaseModel):
    """聊天请求模型"""
    messages: List[ChatMessage]
    voice: str = "Cherry"
    audio_format: str = "wav"


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str
    audio_url: Optional[str] = None
    success: bool = True
    error: Optional[str] = None

class WebSocketChatRequest(BaseModel):
    """WebSocket聊天请求模型"""
    websocket_id: str
    messages: List[ChatMessage]
    voice: str = "Cherry"
    audio_format: str = "wav"
    message_type: str = "chat"  # 消息类型字段，默认为"chat"

class MaterialUploadRequest(BaseModel):
    """材料上传转发的HTTP请求模型"""
    websocket_id: str
    data: Any
    message_type: str = "materialsup"

class TableFormRequest(BaseModel):
    """表单处理的HTTP请求模型"""
    messages: List[Dict[str, Any]]  # 支持复杂的消息结构，包含文本、图像和视频
    form_template: Dict[str, Any] = None  # 可选的表单模板，如果不提供则使用默认模板
