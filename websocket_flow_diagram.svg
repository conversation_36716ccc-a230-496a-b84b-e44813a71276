<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .box { fill: #e1f5fe; stroke: #01579b; stroke-width: 2; rx: 8; }
      .endpoint { fill: #f3e5f5; stroke: #4a148c; stroke-width: 2; rx: 8; }
      .service { fill: #fff3e0; stroke: #e65100; stroke-width: 2; rx: 8; }
      .ai { fill: #e8f5e8; stroke: #1b5e20; stroke-width: 2; rx: 8; }
      .response { fill: #fce4ec; stroke: #880e4f; stroke-width: 2; rx: 8; }
      .decision { fill: #fff9c4; stroke: #f57f17; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .label { font-family: Arial, sans-serif; font-size: 10px; fill: #666; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 前端客户端 -->
  <rect class="box" x="50" y="50" width="120" height="60"/>
  <text class="text" x="110" y="80">前端客户端</text>
  
  <!-- WebSocket端点 -->
  <rect class="endpoint" x="300" y="50" width="120" height="60"/>
  <text class="text" x="360" y="80">/ws/chat</text>
  
  <!-- WebSocket处理器 -->
  <rect class="service" x="550" y="50" width="140" height="60"/>
  <text class="text" x="620" y="80">WebSocketHandler</text>
  
  <!-- HTTP端点 -->
  <rect class="endpoint" x="50" y="200" width="150" height="60"/>
  <text class="text" x="125" y="230">/api/chat_to_websocket</text>
  
  <!-- 材料上传端点 -->
  <rect class="endpoint" x="50" y="300" width="150" height="60"/>
  <text class="text" x="125" y="330">/api/forward_to_websocket</text>
  
  <!-- 消息处理 -->
  <rect class="service" x="350" y="200" width="120" height="60"/>
  <text class="text" x="410" y="230">处理聊天消息</text>
  
  <!-- 消息类型判断 -->
  <polygon class="decision" points="410,320 470,340 410,360 350,340"/>
  <text class="text" x="410" y="340">消息类型</text>
  
  <!-- 聊天模式 -->
  <rect class="service" x="250" y="420" width="100" height="60"/>
  <text class="text" x="300" y="450">普通聊天模式</text>
  
  <!-- 表单模式 -->
  <rect class="service" x="420" y="420" width="100" height="60"/>
  <text class="text" x="470" y="450">表单提取模式</text>
  
  <!-- AI服务 -->
  <rect class="ai" x="320" y="520" width="120" height="60"/>
  <text class="text" x="380" y="550">AIService处理</text>
  
  <!-- 视频转换 -->
  <rect class="service" x="500" y="520" width="120" height="60"/>
  <text class="text" x="560" y="550">VideoConverter</text>
  
  <!-- 千问模型 -->
  <rect class="ai" x="650" y="520" width="120" height="60"/>
  <text class="text" x="710" y="550">千问多模态模型</text>
  
  <!-- 文本响应 -->
  <rect class="response" x="580" y="650" width="100" height="60"/>
  <text class="text" x="630" y="680">文本响应</text>
  
  <!-- 音频响应 -->
  <rect class="response" x="720" y="650" width="100" height="60"/>
  <text class="text" x="770" y="680">音频响应</text>
  
  <!-- 前端接收 -->
  <rect class="response" x="850" y="600" width="100" height="60"/>
  <text class="text" x="900" y="630">前端接收文本</text>
  
  <rect class="response" x="850" y="700" width="100" height="60"/>
  <text class="text" x="900" y="730">前端接收音频</text>
  
  <!-- 连接管理 -->
  <rect class="service" x="1000" y="50" width="120" height="60"/>
  <text class="text" x="1060" y="80">连接管理器</text>
  
  <!-- 连接箭头和标签 -->
  <!-- 1. 建立连接 -->
  <line class="arrow" x1="170" y1="80" x2="300" y2="80"/>
  <text class="label" x="235" y="75">1. 建立WebSocket连接</text>
  
  <!-- 2. 初始化 -->
  <line class="arrow" x1="420" y1="80" x2="550" y2="80"/>
  <text class="label" x="485" y="75">2. 发送init_client</text>
  
  <!-- 3. 返回ID -->
  <line class="arrow" x1="550" y1="90" x2="420" y2="90"/>
  <text class="label" x="485" y="105">3. 返回connection_id</text>
  
  <!-- 4a. 直接WebSocket -->
  <line class="arrow" x1="170" y1="100" x2="350" y2="220"/>
  <text class="label" x="250" y="160">4a. 直接WebSocket消息</text>
  
  <!-- 4b. HTTP请求 -->
  <line class="arrow" x1="200" y1="230" x2="350" y2="230"/>
  <text class="label" x="275" y="225">4b. HTTP请求</text>
  
  <!-- 4c. 材料上传 -->
  <line class="arrow" x1="200" y1="330" x2="350" y2="250"/>
  <text class="label" x="275" y="290">4c. 材料上传</text>
  
  <!-- 5. 消息类型判断 -->
  <line class="arrow" x1="410" y1="260" x2="410" y2="320"/>
  <text class="label" x="430" y="290">5. 解析消息类型</text>
  
  <!-- 6a. chat模式 -->
  <line class="arrow" x1="380" y1="350" x2="320" y2="420"/>
  <text class="label" x="340" y="385">chat模式</text>
  
  <!-- 6b. table模式 -->
  <line class="arrow" x1="440" y1="350" x2="480" y2="420"/>
  <text class="label" x="470" y="385">table模式</text>
  
  <!-- 7. AI处理 -->
  <line class="arrow" x1="350" y1="480" x2="380" y2="520"/>
  <line class="arrow" x1="470" y1="480" x2="380" y2="520"/>
  
  <!-- 8. 视频转换 -->
  <line class="arrow" x1="440" y1="550" x2="500" y2="550"/>
  <text class="label" x="470" y="545">7. 视频格式转换</text>
  
  <!-- 9. 调用模型 -->
  <line class="arrow" x1="620" y1="550" x2="650" y2="550"/>
  <text class="label" x="635" y="545">8. 调用千问模型</text>
  
  <!-- 10. 流式响应 -->
  <line class="arrow" x1="680" y1="580" x2="630" y2="650"/>
  <text class="label" x="655" y="615">9a. 流式文本</text>
  
  <line class="arrow" x1="740" y1="580" x2="770" y2="650"/>
  <text class="label" x="755" y="615">9b. 流式音频</text>
  
  <!-- 11. 前端接收 -->
  <line class="arrow" x1="680" y1="680" x2="850" y2="630"/>
  <text class="label" x="765" y="655">10a. 实时推送文本</text>
  
  <line class="arrow" x1="820" y1="680" x2="850" y2="730"/>
  <text class="label" x="835" y="705">10b. 实时推送音频</text>
  
  <!-- 连接管理 -->
  <line class="arrow" x1="1060" y1="110" x2="1060" y2="150"/>
  <text class="label" x="1080" y="130">定期清理超时连接</text>
  
</svg>
