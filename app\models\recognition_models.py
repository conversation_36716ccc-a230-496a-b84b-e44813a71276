from typing import Dict, List, Optional
from pydantic import BaseModel, Field, validator  # 添加 validator 导入
from fastapi import File, Form
from fastapi import APIRouter, HTTPException, File, UploadFile, Form


class RecognizeLicenseByBinaryRequest(BaseModel):
    """通过Base64数据识别证照请求模型"""
    binary_data: str = Field(..., description="图片二进制数据")
    mime_type: str = Field(..., description="图片MIME类型")
    prompt: Optional[str] = Field("", description="识别提示词")
    additional_prompt: Optional[str] = Field("", description="附加提示词")

class RecognizeLicenseByUrlRequest(BaseModel):
    """通过URL识别证照请求模型"""
    image_url: str = Field(..., description="图片URL地址")
    prompt: str = Field("", description="识别提示词")
    additional_prompt: str = Field("", description="附加提示词")

# 移除以下类定义
# class RecognizeLicenseByBinaryForm(BaseModel):
#     """通过上传文件识别证照请求模型"""
#     binary_data: UploadFile = File(...,description="图片文件")
#     prompt: str = Form('',description="识别提示词")
#     additional_prompt: str = Form('',description="附加识别提示词")

class RecognitionResponse(BaseModel):
    code: int
    info: str
    data: Optional[dict] = None


class XformData(BaseModel):
    name: str
    idNumber: str
    gender: str

class XformItem(BaseModel):
    xformCode: str
    xformName: str
    xformData: List[XformData]

class ApplicationInfo(BaseModel):
    businessId: str
    sessionId: str
    itemName: str
    itemType: str
    verificationType: int = Field(..., ge=1, le=2, description="只能为 1 或者 2")

class FileInfo(BaseModel):
    fileName: str
    fileCode: str
    downloadUrl: str

class MaterialItem(BaseModel):
    materialCode: str
    materialTypeName: str
    fileList: List[FileInfo]

class PromptItem(BaseModel):
    materialCode: str
    materialTypeName: str
    prompt: str
    additional_prompt: str

class BatchMaterialVerificationRequest(BaseModel):
    xformList: Optional[List[XformItem]] = None
    applicationInfo: ApplicationInfo
    materialList: List[MaterialItem] = Field(..., min_items=1)
    promptList: List[PromptItem]

    @validator('xformList')
    def validate_xform_list(cls, v, values):
        if values.get('applicationInfo', {}).get('verificationType') == 2 and not v:
            raise ValueError("当 verificationType 为 2 时，xformList 不能为空")
        return v
    