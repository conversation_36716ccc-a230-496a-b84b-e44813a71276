#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用验证工具模块
用于验证新生儿办证相关数据的完整性和有效性
"""

from typing import Dict, List


def validate_application_info(data: Dict) -> List[str]:
    """
    验证 applicationInfo 对应的对象及属性是否存在
    
    Args:
        data: 包含 applicationInfo 的数据字典
        
    Returns:
        List[str]: 缺失字段的列表
    """
    missing_fields = []
    
    if 'applicationInfo' not in data:
        missing_fields.append('applicationInfo')
    else:
        app_info = data['applicationInfo']
        if app_info is None:
            missing_fields.append('applicationInfo')
        else:
            required_app_fields = ['businessId', 'sessionId', 'itemName', 'itemType', 'verificationType']
            for field in required_app_fields:
                if field not in app_info:
                    missing_fields.append(f'applicationInfo.{field}')
                    
    return missing_fields


def validate_material_list(data: Dict) -> List[str]:
    """
    验证 materialList 对象属性是否存在，且 fileList 必须大于 0
    
    Args:
        data: 包含 materialList 的数据字典
        
    Returns:
        List[str]: 缺失字段的列表
    """
    missing_fields = []
    
    if 'materialList' not in data:
        missing_fields.append('materialList')
    elif not data['materialList']:
        missing_fields.append('materialList 不能为空')
    else:
        for idx, material in enumerate(data['materialList']):
            if 'materialCode' not in material:
                missing_fields.append(f'materialList[{idx}].materialCode')
            if 'materialTypeName' not in material:
                missing_fields.append(f'materialList[{idx}].materialTypeName')
            if 'fileList' not in material:
                missing_fields.append(f'materialList[{idx}].fileList')
            elif not material['fileList']:
                missing_fields.append(f'materialList[{idx}].fileList 不能为空')
            else:
                for file_idx, file_info in enumerate(material['fileList']):
                    if 'fileName' not in file_info:
                        missing_fields.append(f'materialList[{idx}].fileList[{file_idx}].fileName')
                    if 'fileCode' not in file_info:
                        missing_fields.append(f'materialList[{idx}].fileList[{file_idx}].fileCode')
                    if 'downloadUrl' not in file_info:
                        missing_fields.append(f'materialList[{idx}].fileList[{file_idx}].downloadUrl')
                        
    return missing_fields


def validate_xform_list(data: Dict) -> List[str]:
    """
    当 verificationType 为 2 时，验证 xformList 必须存在且 list 对象必须大于 0
    
    Args:
        data: 包含 xformList 的数据字典
        
    Returns:
        List[str]: 缺失字段的列表
    """
    missing_fields = []
    
    if 'applicationInfo' in data and data['applicationInfo'].get('verificationType') == 2:
        if 'xformList' not in data:
            missing_fields.append('xformList')
        elif data['xformList'] is None or not data['xformList']:
            missing_fields.append('xformList 不能为空')
        else:
            for xform_idx, xform in enumerate(data['xformList']):
                if 'xformCode' not in xform:
                    missing_fields.append(f'xformList[{xform_idx}].xformCode')
                if 'xformName' not in xform:
                    missing_fields.append(f'xformList[{xform_idx}].xformName')
                if 'xformData' not in xform:
                    missing_fields.append(f'xformList[{xform_idx}].xformData')
                    
    return missing_fields


def validate_newborn_certificate_data(data: Dict) -> List[str]:
    """
    验证新生儿办证数据的完整性
    
    Args:
        data: 新生儿办证数据
        
    Returns:
        List[str]: 缺失字段的列表
    """
    missing_fields = []
    
    # 验证各个部分
    missing_fields.extend(validate_application_info(data))
    missing_fields.extend(validate_material_list(data))
    missing_fields.extend(validate_xform_list(data))
    
    return missing_fields


def validate_image_url(image_path: str) -> bool:
    """
    验证图片URL是否有效
    
    Args:
        image_path: 图片路径或URL
        
    Returns:
        bool: 是否为有效的URL
    """
    return image_path.startswith(('http://', 'https://'))


def validate_base64_data(base64_data: str) -> bool:
    """
    验证Base64数据是否有效
    
    Args:
        base64_data: Base64编码的数据
        
    Returns:
        bool: 是否为有效的Base64数据
    """
    try:
        import base64
        base64.b64decode(base64_data)
        return True
    except Exception:
        return False
