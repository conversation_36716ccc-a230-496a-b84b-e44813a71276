#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import logging
from fastapi import FastAPI, WebSocket, Request
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError

from app.config.settings import (
    APP_TITLE, APP_HOST, APP_PORT, setup_logging,
    CORS_ORIGINS, CORS_CREDENTIALS, CORS_METHODS, CORS_HEADERS
)
from app.services.websocket_manager import WebSocketConnectionManager
from app.routes.websocket import WebSocketHandler
from app.routes.chat import create_chat_router
from app.routes.ocr import create_ocr_router
from app.routes.table import create_table_router
from app.routes.management import create_management_router
#from app.routes.newborn import router as newborn_router
from app.routes.recognition_routes import router as recognition_router
from app.utils.file_utils import ensure_directories, cleanup_old_temp_files

# 配置日志
logger = setup_logging()

# 创建FastAPI应用
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 应用启动时执行的操作
    logger.info("应用启动，清理旧的临时文件")
    cleanup_old_temp_files()

    # 启动WebSocket连接清理任务
    logger.info("启动WebSocket连接清理任务")
    asyncio.create_task(websocket_manager.start_cleanup_task())
    yield
    # 应用关闭时可添加清理逻辑
    # 此处可按需补充关闭时的清理代码

app = FastAPI(title=APP_TITLE, lifespan=lifespan)

# 添加异常处理器（兼容gb2b_agent项目）
@app.exception_handler(RequestValidationError)
def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误"""
    error = exc.errors()[0]
    error_info = {
        "error_code": 400,
        "error_message": error['msg']
    }
    return JSONResponse(status_code=400, content=error_info)

# 添加CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=CORS_CREDENTIALS,
    allow_methods=CORS_METHODS,
    allow_headers=CORS_HEADERS,
)

# 确保目录存在
ensure_directories()

# 初始化WebSocket连接管理器
websocket_manager = WebSocketConnectionManager()
websocket_handler = WebSocketHandler(websocket_manager)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 注册路由
app.include_router(create_management_router(websocket_manager))
app.include_router(create_chat_router(websocket_manager))
app.include_router(create_ocr_router())
app.include_router(create_table_router())
# 证照识别路由
#app.include_router(newborn_router)
app.include_router(recognition_router)

# WebSocket端点，用于实时音频和文字流
@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    await websocket_handler.handle_websocket_chat(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host=APP_HOST, port=APP_PORT, reload=True)
