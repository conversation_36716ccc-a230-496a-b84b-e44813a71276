#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from fastapi import APIRouter
from fastapi.responses import JSONResponse, HTMLResponse

from app.services.websocket_manager import WebSocketConnectionManager

logger = logging.getLogger(__name__)

def create_management_router(websocket_manager: WebSocketConnectionManager) -> APIRouter:
    """创建管理路由"""
    router = APIRouter()
    
    @router.get("/", response_class=HTMLResponse)
    async def root():
        """返回主页"""
        try:
            with open("static/index.html", "r", encoding="utf-8") as f:
                html_content = f.read()
            return HTMLResponse(content=html_content)
        except FileNotFoundError:
            return HTMLResponse(content="<h1>欢迎使用千问多模态流式API</h1>")
    
    @router.get("/api/active_connections")
    async def get_active_connections():
        """获取所有活跃的WebSocket连接ID和状态信息"""
        connection_info = websocket_manager.get_connection_info()
        
        return {
            "status": "success",
            **connection_info
        }
    
    @router.post("/api/cleanup_connections")
    async def cleanup_connections():
        """手动触发清理不活跃的WebSocket连接"""
        # 记录清理前的连接数
        before_count = len(websocket_manager.active_connections)
        
        # 执行清理
        await websocket_manager.cleanup_inactive_connections()
        
        # 记录清理后的连接数
        after_count = len(websocket_manager.active_connections)
        
        return {
            "status": "success",
            "message": "已清理不活跃的WebSocket连接",
            "connections_before": before_count,
            "connections_after": after_count,
            "connections_removed": before_count - after_count
        }
    
    return router
