#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import base64
import requests
import logging
from typing import Optional

from app.config.settings import OCR_API_KEY, OCR_UPLOAD_URL, OCR_WORKFLOW_URL, TEMP_DIR
from app.utils.file_utils import cleanup_temp_files

logger = logging.getLogger(__name__)

class OCRService:
    """OCR识别服务类"""
    
    def __init__(self):
        self.api_key = OCR_API_KEY
        self.upload_url = OCR_UPLOAD_URL
        self.workflow_url = OCR_WORKFLOW_URL
    
    def process_image(self, image_data: bytes, content_type: str, 
                     name: str, data_type: str, id_number: Optional[str] = None):
        """
        处理图像OCR识别
        """
        # 保存图片到临时文件
        file_ext = content_type.split('/')[-1]
        temp_path = f"{TEMP_DIR}/ocr_image_{int(time.time())}.{file_ext}"
        
        with open(temp_path, "wb") as f:
            f.write(image_data)
        
        logger.info(f"图片已保存到临时文件: {temp_path}")
        
        try:
            # 第一步：上传文件获取file_id
            file_id = self._upload_file(temp_path, content_type)
            
            # 第二步：使用file_id调用OCR识别接口
            result = self._recognize_image(file_id, name, data_type, id_number)
            
            return result
            
        finally:
            # 清理临时文件
            cleanup_temp_files(temp_path, delay=0)  # 立即清理
    
    def _upload_file(self, file_path: str, content_type: str) -> str:
        """
        上传文件并返回file_id
        """
        logger.info("第一步：上传图片文件获取file_id")
        
        # 准备文件和参数
        files = {
            'file': (os.path.basename(file_path), open(file_path, 'rb'), content_type)
        }
        data = {
            'user': 'abc-123'
        }
        headers = {
            'Authorization': f'Bearer {self.api_key}'
        }
        
        try:
            # 发送第一个请求上传文件
            upload_response = requests.post(
                self.upload_url, 
                files=files,
                data=data,
                headers=headers
            )
            
            # 关闭文件
            files['file'][1].close()
            
            if upload_response.status_code not in [200, 201]:
                logger.error(f"文件上传失败，状态码: {upload_response.status_code}, 响应: {upload_response.text[:200]}")
                raise Exception(f"文件上传失败: {upload_response.text}")
            
            # 从上传响应中提取file_id
            upload_result = upload_response.json()
            file_id = upload_result.get('id')
            
            if not file_id:
                logger.error("上传成功但未获取到file_id")
                raise Exception("上传成功但未获取到文件ID")
            
            logger.info(f"文件上传成功，获取到file_id: {file_id}")
            return file_id
            
        except requests.RequestException as e:
            logger.error(f"OCR服务请求异常: {str(e)}")
            raise Exception(f"调用OCR服务时发生网络错误: {str(e)}")
    
    def _recognize_image(self, file_id: str, name: str, data_type: str, 
                        id_number: Optional[str] = None) -> dict:
        """
        使用file_id调用OCR识别接口
        """
        logger.info("第二步：调用OCR识别服务")
        
        # 准备第二个API调用的参数
        inputs = {
            "inputs": {
                "image": {
                    "type": "image",
                    "transfer_method": "local_file",
                    "upload_file_id": file_id
                },
                "name": name,
                "dataType": data_type
            },
            "user": "abc-123"
        }
        
        # 只有当idNumber有值时才添加到inputs中
        if id_number:
            inputs["inputs"]["idNumber"] = id_number
            logger.info("包含idNumber参数在请求中")
        else:
            logger.info("idNumber参数为空，不包含在请求中")
        
        headers = {
            'Authorization': f'Bearer {self.api_key}'
        }
        
        # 发送第二个请求进行OCR识别
        ocr_response = requests.post(
            self.workflow_url,
            json=inputs,
            headers=headers
        )
        
        if ocr_response.status_code not in [200, 201]:
            logger.error(f"OCR识别失败，状态码: {ocr_response.status_code}, 响应: {ocr_response.text[:200]}")
            raise Exception(f"OCR识别失败: {ocr_response.text}")
        
        # 解析OCR识别结果
        ocr_result = ocr_response.json()
        logger.info(f"OCR识别完成，结果: {ocr_result}")
        
        # 提取is_success字段
        is_success = "False"
        outmessage = "识别完成"
        
        if ocr_result and "data" in ocr_result and "outputs" in ocr_result["data"]:
            is_success = ocr_result["data"]["outputs"].get("is_success", "False")
            outmessage = ocr_result["data"]["outputs"].get("output", "识别完成")
        
        return {
            "code": 200,    
            "status": "success",
            "is_success": is_success,
            "message": outmessage
        }
    
    def parse_base64_image(self, image_base64: str) -> tuple:
        """
        解析Base64图像数据
        返回 (image_data, content_type)
        """
        try:
            # 检查是否包含data URI scheme
            if image_base64.startswith('data:'):
                # 提取MIME类型和Base64数据
                header, encoded = image_base64.split(",", 1)
                content_type = header.split(";")[0].split(":")[1]
                image_data = base64.b64decode(encoded)
            else:
                # 没有data URI，尝试直接解码
                image_data = base64.b64decode(image_base64)
                # 默认为JPEG格式
                content_type = "image/jpeg"
            
            return image_data, content_type
            
        except Exception as e:
            logger.error(f"Base64图片解码失败: {str(e)}")
            raise Exception(f"Base64图片格式错误: {str(e)}")
