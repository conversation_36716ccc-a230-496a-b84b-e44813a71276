#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import re
import logging
from typing import List, Dict, Any, Optional

from app.services.ai_service import AIService
from app.config.prompts import TABLE_PROMPT

logger = logging.getLogger(__name__)

class TableService:
    """表单处理服务类"""
    
    def __init__(self):
        self.ai_service = AIService()
    
    def process_form_data(self, messages: List[Dict[str, Any]], 
                         form_template: Optional[Dict[str, Any]] = None) -> dict:
        """
        处理多模态表单数据（支持图片和视频），返回JSON格式结果
        """
        logger.info(f"收到表单处理请求，消息数量: {len(messages)}")
        
        # 处理视频消息
        processed_messages = self.ai_service.process_video_content(messages)
        
        # 选择提示词模板
        if form_template:
            prompt_messages = self._create_custom_template(form_template) + processed_messages
        else:
            prompt_messages = TABLE_PROMPT + processed_messages
        
        try:
            # 调用千问多模态模型，请求文本和音频返回
            completion = self.ai_service.create_completion(
                messages=prompt_messages,
                modalities=["text", "audio"],
                voice="Cherry",
                audio_format="wav",
                stream=True
            )
            
            text_response = ""
            full_audio_data = b''
            
            # 处理流式响应
            for chunk in completion:
                if chunk.choices:
                    # 处理音频内容
                    if hasattr(chunk.choices[0].delta, "audio"):
                        # 从音频数据中提取
                        if "data" in chunk.choices[0].delta.audio:
                            audio_string = chunk.choices[0].delta.audio["data"]
                            # 解码base64数据
                            import base64
                            wav_bytes = base64.b64decode(audio_string)
                            # 累积完整的音频数据
                            full_audio_data += wav_bytes
                        
                        # 处理文字内容
                        if "transcript" in chunk.choices[0].delta.audio:
                            transcript = chunk.choices[0].delta.audio["transcript"]
                            text_response += transcript
            
            # 解析响应结果
            return self._parse_response(text_response)
            
        except Exception as e:
            import traceback
            error_msg = f"{str(e)}\n{traceback.format_exc()}"
            logger.error(f"处理表单请求时出错: {error_msg}")
            raise Exception(f"处理表单数据时出错: {str(e)}")
    
    def _create_custom_template(self, form_template: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        创建自定义表单模板提示词
        """
        return [
            {
                "role": "user",
                "content": (
                    "现在你是一个严格的材料信息提取引擎，请严格遵守以下规则：\n"
                    "1. 当用户上传图片或视频时，准确识别其中的材料信息（不限于身份证）\n"
                    "2. 仅当材料中的关键信息清晰可识别时，才允许填写对应字段\n"
                    "3. 根据我提供的表单模板结构，将识别结果智能映射到对应字段\n"
                    "4. 输出必须为严格规范的JSON数组，每个元素包含name、id和value三个字段\n"
                    "5. 未识别字段的value必须且只能设置为空字符串（\"\"）\n"
                    "6. 支持自动格式转换（如日期标准化、单位统一等）\n"
                    "7. 只能输出纯JSON格式数据，禁止输出任何解释、说明或额外文本\n"
                )
            },
            {
                "role": "assistant",
                "content": (
                    "明白，我将执行以下处理流程：\n"
                    "1. OCR识别材料所有文字和结构化数据\n"
                    "2. 智能匹配表单字段（如：'产品名称'→'productName', '规格参数'→'specs'等）\n"
                    "3. 自动转换格式（日期→YYYY-MM-DD，金额→元，单位→标准计量单位等）\n"
                    "4. 输出格式示例：[\n"
                    "  {\"name\":\"材料编号\",\"id\":\"field_001\",\"value\":\"M2023-0856\"},\n"
                    "  {\"name\":\"检测结果\",\"id\":\"field_002\",\"value\":\"合格\"}\n"
                    "]\n"
                    "请提供材料图片和表单模板说明。"
                )
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "表单模板及字段说明："},
                    {"type": "text", "text": json.dumps({
                        "form_template": form_template,
                        "field_descriptions": {
                            "name": "字段显示名称",
                            "id": "表单系统字段ID",
                        }
                    })}
                ]
            }
        ]
    
    def _parse_response(self, text_response: str) -> dict:
        """
        解析AI响应文本为JSON格式
        """
        try:
            # 检查是否包含Markdown代码块
            json_pattern = r'```(?:json)?\s*([\s\S]*?)\s*```'
            match = re.search(json_pattern, text_response)
            
            if match:
                # 提取代码块中的JSON内容
                json_content = match.group(1)
                parsed_data = json.loads(json_content)
            else:
                # 直接尝试解析
                parsed_data = json.loads(text_response)
            
            # 检查JSON数据是否所有value都为空
            all_values_empty = False
            if isinstance(parsed_data, list) and len(parsed_data) > 0:
                all_values_empty = all(
                    (isinstance(item, dict) and 
                     'value' in item and 
                     (item['value'] == '' or item['value'] is None))
                    for item in parsed_data
                )
            
            if all_values_empty:
                return {
                    "code": 404,
                    "status": "partial_success",
                    "message": "模型返回的所有字段值都为空",
                    "data": parsed_data
                }
            else:
                return {
                    "code": 200,
                    "status": "success", 
                    "data": parsed_data
                }
                
        except json.JSONDecodeError as e:
            # 如果解析JSON失败，返回原始文本
            return {
                "code": 404,
                "status": "partial_success",
                "message": f"模型返回的数据不是有效的JSON格式: {str(e)}",
                "data": text_response
            }
